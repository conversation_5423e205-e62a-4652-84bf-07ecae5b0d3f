# Server Configuration
server.port=8080
server.servlet.context-path=/

# Application Configuration
spring.application.name=qrcode-barcode-generator

# Logging Configuration
logging.level.com.hemakesh.qrcode=INFO
logging.level.org.springframework.web=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Actuator Configuration
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always

# API Documentation
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# QR Code Configuration
qrcode.max-content-length=2000
qrcode.default-size=300
qrcode.default-format=PNG

# Barcode Configuration
barcode.max-content-length=100
barcode.default-width=300
barcode.default-height=100
barcode.default-format=PNG
