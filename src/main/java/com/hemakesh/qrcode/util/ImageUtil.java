package com.hemakesh.qrcode.util;

import com.google.zxing.common.BitMatrix;
import org.apache.batik.dom.GenericDOMImplementation;
import org.apache.batik.svggen.SVGGraphics2D;
import org.springframework.stereotype.Component;
import org.w3c.dom.DOMImplementation;
import org.w3c.dom.Document;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.StringWriter;

/**
 * Utility class for image processing and format conversion
 */
@Component
public class ImageUtil {
    
    private static final int BLACK = 0xFF000000;
    private static final int WHITE = 0xFFFFFFFF;
    
    /**
     * Converts BitMatrix to BufferedImage
     */
    public BufferedImage toBufferedImage(BitMatrix matrix) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, matrix.get(x, y) ? BLACK : WHITE);
            }
        }
        
        return image;
    }
    
    /**
     * Converts BufferedImage to PNG byte array
     */
    public byte[] toPngBytes(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        return baos.toByteArray();
    }
    
    /**
     * Converts BitMatrix to SVG string
     */
    public String toSvg(BitMatrix matrix) throws IOException {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        
        // Create SVG document
        DOMImplementation domImpl = GenericDOMImplementation.getDOMImplementation();
        String svgNS = "http://www.w3.org/2000/svg";
        Document document = domImpl.createDocument(svgNS, "svg", null);
        
        // Create SVG graphics
        SVGGraphics2D svgGenerator = new SVGGraphics2D(document);
        svgGenerator.setSVGCanvasSize(new Dimension(width, height));
        
        // Set white background
        svgGenerator.setColor(Color.WHITE);
        svgGenerator.fillRect(0, 0, width, height);
        
        // Draw black squares for true bits
        svgGenerator.setColor(Color.BLACK);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                if (matrix.get(x, y)) {
                    svgGenerator.fillRect(x, y, 1, 1);
                }
            }
        }
        
        // Convert to string
        StringWriter writer = new StringWriter();
        svgGenerator.stream(writer, true);
        return writer.toString();
    }
    
    /**
     * Creates a BufferedImage with text below the barcode
     */
    public BufferedImage addTextToImage(BufferedImage barcodeImage, String text, boolean includeText) {
        if (!includeText || text == null || text.trim().isEmpty()) {
            return barcodeImage;
        }
        
        int barcodeWidth = barcodeImage.getWidth();
        int barcodeHeight = barcodeImage.getHeight();
        
        // Calculate text dimensions
        Font font = new Font(Font.SANS_SERIF, Font.PLAIN, 12);
        BufferedImage tempImage = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
        Graphics2D tempG2d = tempImage.createGraphics();
        tempG2d.setFont(font);
        FontMetrics fontMetrics = tempG2d.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(text);
        int textHeight = fontMetrics.getHeight();
        tempG2d.dispose();
        
        // Create new image with space for text
        int totalWidth = Math.max(barcodeWidth, textWidth);
        int totalHeight = barcodeHeight + textHeight + 5; // 5px padding
        BufferedImage combinedImage = new BufferedImage(totalWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
        
        Graphics2D g2d = combinedImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // Fill background with white
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, totalWidth, totalHeight);
        
        // Draw barcode centered
        int barcodeX = (totalWidth - barcodeWidth) / 2;
        g2d.drawImage(barcodeImage, barcodeX, 0, null);
        
        // Draw text centered below barcode
        g2d.setColor(Color.BLACK);
        g2d.setFont(font);
        int textX = (totalWidth - textWidth) / 2;
        int textY = barcodeHeight + fontMetrics.getAscent() + 5;
        g2d.drawString(text, textX, textY);
        
        g2d.dispose();
        return combinedImage;
    }
    
    /**
     * Resizes an image to the specified dimensions
     */
    public BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resizedImage.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();
        
        return resizedImage;
    }
    
    /**
     * Gets the appropriate content type for the format
     */
    public String getContentType(String format) {
        switch (format.toUpperCase()) {
            case "PNG":
                return "image/png";
            case "SVG":
                return "image/svg+xml";
            default:
                throw new IllegalArgumentException("Unsupported format: " + format);
        }
    }
    
    /**
     * Gets the appropriate file extension for the format
     */
    public String getFileExtension(String format) {
        switch (format.toUpperCase()) {
            case "PNG":
                return ".png";
            case "SVG":
                return ".svg";
            default:
                throw new IllegalArgumentException("Unsupported format: " + format);
        }
    }
}
