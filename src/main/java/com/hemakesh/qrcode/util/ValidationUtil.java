package com.hemakesh.qrcode.util;

import com.hemakesh.qrcode.exception.ValidationException;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Utility class for input validation
 */
@Component
public class ValidationUtil {
    
    private static final Pattern URL_PATTERN = Pattern.compile(
            "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$",
            Pattern.CASE_INSENSITIVE
    );
    
    private static final List<String> SUPPORTED_FORMATS = Arrays.asList("PNG", "SVG");
    private static final List<String> SUPPORTED_ERROR_CORRECTION_LEVELS = Arrays.asList("L", "M", "Q", "H");
    private static final List<String> SUPPORTED_BARCODE_FORMATS = Arrays.asList(
            "CODE_128", "CODE_39", "CODE_93", "EAN_8", "EAN_13", "UPC_A", "UPC_E"
    );
    
    /**
     * Validates if the content is a valid URL
     */
    public boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        try {
            new URL(url);
            return URL_PATTERN.matcher(url).matches();
        } catch (MalformedURLException e) {
            return false;
        }
    }
    
    /**
     * Validates multiple URLs separated by comma
     */
    public void validateUrls(String content) {
        if (content == null || content.trim().isEmpty()) {
            throw new ValidationException("Content cannot be empty");
        }
        
        String[] urls = content.split(",");
        for (String url : urls) {
            String trimmedUrl = url.trim();
            if (!trimmedUrl.isEmpty() && !isValidUrl(trimmedUrl)) {
                throw new ValidationException("Invalid URL: " + trimmedUrl, "content", trimmedUrl);
            }
        }
    }
    
    /**
     * Validates content length
     */
    public void validateContentLength(String content, int maxLength) {
        if (content == null) {
            throw new ValidationException("Content cannot be null");
        }
        
        if (content.length() > maxLength) {
            throw new ValidationException(
                    String.format("Content length (%d) exceeds maximum allowed length (%d)", 
                            content.length(), maxLength),
                    "content", content.length()
            );
        }
    }
    
    /**
     * Validates output format
     */
    public void validateFormat(String format) {
        if (format == null || !SUPPORTED_FORMATS.contains(format.toUpperCase())) {
            throw new ValidationException(
                    "Unsupported format: " + format + ". Supported formats: " + SUPPORTED_FORMATS,
                    "format", format
            );
        }
    }
    
    /**
     * Validates QR code error correction level
     */
    public void validateErrorCorrectionLevel(String level) {
        if (level != null && !SUPPORTED_ERROR_CORRECTION_LEVELS.contains(level.toUpperCase())) {
            throw new ValidationException(
                    "Unsupported error correction level: " + level + 
                    ". Supported levels: " + SUPPORTED_ERROR_CORRECTION_LEVELS,
                    "errorCorrectionLevel", level
            );
        }
    }
    
    /**
     * Validates barcode format
     */
    public void validateBarcodeFormat(String format) {
        if (format != null && !SUPPORTED_BARCODE_FORMATS.contains(format.toUpperCase())) {
            throw new ValidationException(
                    "Unsupported barcode format: " + format + 
                    ". Supported formats: " + SUPPORTED_BARCODE_FORMATS,
                    "barcodeFormat", format
            );
        }
    }
    
    /**
     * Validates dimensions
     */
    public void validateDimensions(Integer width, Integer height, int minWidth, int maxWidth, 
                                 int minHeight, int maxHeight) {
        if (width != null) {
            if (width < minWidth || width > maxWidth) {
                throw new ValidationException(
                        String.format("Width must be between %d and %d pixels", minWidth, maxWidth),
                        "width", width
                );
            }
        }
        
        if (height != null) {
            if (height < minHeight || height > maxHeight) {
                throw new ValidationException(
                        String.format("Height must be between %d and %d pixels", minHeight, maxHeight),
                        "height", height
                );
            }
        }
    }
    
    /**
     * Sanitizes content by removing potentially harmful characters
     */
    public String sanitizeContent(String content) {
        if (content == null) {
            return null;
        }
        
        // Remove control characters except tab, newline, and carriage return
        return content.replaceAll("[\\p{Cntrl}&&[^\t\n\r]]", "");
    }
    
    /**
     * Checks if content appears to be URL(s)
     */
    public boolean isUrlContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        String[] parts = content.split(",");
        for (String part : parts) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty() && !isValidUrl(trimmed)) {
                return false;
            }
        }
        return true;
    }
}
