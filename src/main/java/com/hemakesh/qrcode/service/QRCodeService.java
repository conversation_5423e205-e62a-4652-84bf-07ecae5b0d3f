package com.hemakesh.qrcode.service;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.hemakesh.qrcode.dto.QRCodeRequest;
import com.hemakesh.qrcode.exception.CodeGenerationException;
import com.hemakesh.qrcode.util.ImageUtil;
import com.hemakesh.qrcode.util.ValidationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for QR Code generation
 */
@Service
public class QRCodeService {
    
    private static final Logger logger = LoggerFactory.getLogger(QRCodeService.class);
    
    @Autowired
    private ValidationUtil validationUtil;
    
    @Autowired
    private ImageUtil imageUtil;
    
    @Value("${qrcode.max-content-length:2000}")
    private int maxContentLength;
    
    @Value("${qrcode.default-size:300}")
    private int defaultSize;
    
    /**
     * Generates QR code as byte array
     */
    public byte[] generateQRCode(QRCodeRequest request) {
        logger.info("Generating QR code for request: {}", request);
        
        try {
            // Validate request
            validateRequest(request);
            
            // Sanitize content
            String sanitizedContent = validationUtil.sanitizeContent(request.getContent());
            
            // Generate QR code
            BitMatrix bitMatrix = createQRCodeBitMatrix(sanitizedContent, request);
            
            // Convert to appropriate format
            if ("SVG".equalsIgnoreCase(request.getFormat())) {
                String svgContent = imageUtil.toSvg(bitMatrix);
                return svgContent.getBytes();
            } else {
                BufferedImage image = imageUtil.toBufferedImage(bitMatrix);
                return imageUtil.toPngBytes(image);
            }
            
        } catch (WriterException e) {
            logger.error("Error generating QR code: {}", e.getMessage(), e);
            throw new CodeGenerationException("Failed to generate QR code: " + e.getMessage(), e);
        } catch (IOException e) {
            logger.error("Error converting QR code to format: {}", e.getMessage(), e);
            throw new CodeGenerationException("Failed to convert QR code to " + request.getFormat(), e);
        }
    }
    
    /**
     * Creates BitMatrix for QR code
     */
    private BitMatrix createQRCodeBitMatrix(String content, QRCodeRequest request) throws WriterException {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        
        // Set encoding hints
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 1);
        
        // Set error correction level
        ErrorCorrectionLevel errorCorrectionLevel = getErrorCorrectionLevel(request.getErrorCorrectionLevel());
        hints.put(EncodeHintType.ERROR_CORRECTION, errorCorrectionLevel);
        
        // Use provided size or default
        int size = request.getSize() != null ? request.getSize() : defaultSize;
        
        return qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, size, size, hints);
    }
    
    /**
     * Converts string error correction level to enum
     */
    private ErrorCorrectionLevel getErrorCorrectionLevel(String level) {
        if (level == null) {
            return ErrorCorrectionLevel.M; // Default to Medium
        }
        
        switch (level.toUpperCase()) {
            case "L":
                return ErrorCorrectionLevel.L;
            case "M":
                return ErrorCorrectionLevel.M;
            case "Q":
                return ErrorCorrectionLevel.Q;
            case "H":
                return ErrorCorrectionLevel.H;
            default:
                logger.warn("Unknown error correction level: {}, using default M", level);
                return ErrorCorrectionLevel.M;
        }
    }
    
    /**
     * Validates QR code generation request
     */
    private void validateRequest(QRCodeRequest request) {
        if (request == null) {
            throw new CodeGenerationException("Request cannot be null");
        }
        
        // Validate content
        String content = request.getContent();
        if (content == null || content.trim().isEmpty()) {
            throw new CodeGenerationException("Content cannot be empty");
        }
        
        // Validate content length
        validationUtil.validateContentLength(content, maxContentLength);
        
        // Validate format
        validationUtil.validateFormat(request.getFormat());
        
        // Validate error correction level
        validationUtil.validateErrorCorrectionLevel(request.getErrorCorrectionLevel());
        
        // Validate size
        if (request.getSize() != null) {
            validationUtil.validateDimensions(request.getSize(), request.getSize(), 100, 1000, 100, 1000);
        }
        
        // If content appears to be URLs, validate them
        if (validationUtil.isUrlContent(content)) {
            validationUtil.validateUrls(content);
        }
        
        logger.debug("QR code request validation passed for content length: {}", content.length());
    }
    
    /**
     * Gets the content type for the response
     */
    public String getContentType(String format) {
        return imageUtil.getContentType(format);
    }
    
    /**
     * Gets the filename for download
     */
    public String getFilename(String format) {
        return "qrcode" + imageUtil.getFileExtension(format);
    }
}
