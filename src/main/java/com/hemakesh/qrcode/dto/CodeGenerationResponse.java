package com.hemakesh.qrcode.dto;

/**
 * Response DTO for code generation
 */
public class CodeGenerationResponse {
    
    private String message;
    private String format;
    private String contentType;
    private long size;
    private String filename;
    
    // Constructors
    public CodeGenerationResponse() {}
    
    public CodeGenerationResponse(String message, String format, String contentType) {
        this.message = message;
        this.format = format;
        this.contentType = contentType;
    }
    
    // Getters and Setters
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getFormat() {
        return format;
    }
    
    public void setFormat(String format) {
        this.format = format;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public long getSize() {
        return size;
    }
    
    public void setSize(long size) {
        this.size = size;
    }
    
    public String getFilename() {
        return filename;
    }
    
    public void setFilename(String filename) {
        this.filename = filename;
    }
    
    @Override
    public String toString() {
        return "CodeGenerationResponse{" +
                "message='" + message + '\'' +
                ", format='" + format + '\'' +
                ", contentType='" + contentType + '\'' +
                ", size=" + size +
                ", filename='" + filename + '\'' +
                '}';
    }
}
