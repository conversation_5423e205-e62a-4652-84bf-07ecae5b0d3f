package com.hemakesh.qrcode.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for Barcode generation
 */
public class BarcodeRequest extends CodeGenerationRequest {
    
    @Size(max = 100, message = "Barcode content cannot exceed 100 characters")
    private String content;
    
    @Min(value = 200, message = "Width must be at least 200 pixels")
    @Max(value = 800, message = "Width cannot exceed 800 pixels")
    private Integer width = 300;
    
    @Min(value = 50, message = "Height must be at least 50 pixels")
    @Max(value = 200, message = "Height cannot exceed 200 pixels")
    private Integer height = 100;
    
    private String barcodeFormat = "CODE_128"; // CODE_128, CODE_39, EAN_13, etc.
    private boolean includeText = true;
    
    // Constructors
    public BarcodeRequest() {
        super();
    }
    
    public BarcodeRequest(String content, String format, Integer width, Integer height) {
        super(content, format);
        this.width = width;
        this.height = height;
    }
    
    // Get<PERSON> and Setters
    @Override
    public String getContent() {
        return content != null ? content : super.getContent();
    }
    
    @Override
    public void setContent(String content) {
        this.content = content;
        super.setContent(content);
    }
    
    @Override
    public Integer getWidth() {
        return width;
    }
    
    @Override
    public void setWidth(Integer width) {
        this.width = width;
    }
    
    @Override
    public Integer getHeight() {
        return height;
    }
    
    @Override
    public void setHeight(Integer height) {
        this.height = height;
    }
    
    public String getBarcodeFormat() {
        return barcodeFormat;
    }
    
    public void setBarcodeFormat(String barcodeFormat) {
        this.barcodeFormat = barcodeFormat;
    }
    
    public boolean isIncludeText() {
        return includeText;
    }
    
    public void setIncludeText(boolean includeText) {
        this.includeText = includeText;
    }
    
    @Override
    public String toString() {
        return "BarcodeRequest{" +
                "content='" + getContent() + '\'' +
                ", format='" + getFormat() + '\'' +
                ", width=" + width +
                ", height=" + height +
                ", barcodeFormat='" + barcodeFormat + '\'' +
                ", includeText=" + includeText +
                '}';
    }
}
