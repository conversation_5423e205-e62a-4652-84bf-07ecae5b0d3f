package com.hemakesh.qrcode.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for QR Code generation
 */
public class QRCodeRequest extends CodeGenerationRequest {
    
    @Size(max = 2000, message = "QR Code content cannot exceed 2000 characters")
    private String content;
    
    @Min(value = 100, message = "Size must be at least 100 pixels")
    @Max(value = 1000, message = "Size cannot exceed 1000 pixels")
    private Integer size = 300;
    
    private String errorCorrectionLevel = "M"; // L, M, Q, H
    
    // Constructors
    public QRCodeRequest() {
        super();
    }
    
    public QRCodeRequest(String content, String format, Integer size) {
        super(content, format);
        this.size = size;
    }
    
    // Getters and Setters
    @Override
    public String getContent() {
        return content != null ? content : super.getContent();
    }
    
    @Override
    public void setContent(String content) {
        this.content = content;
        super.setContent(content);
    }
    
    public Integer getSize() {
        return size;
    }
    
    public void setSize(Integer size) {
        this.size = size;
    }
    
    public String getErrorCorrectionLevel() {
        return errorCorrectionLevel;
    }
    
    public void setErrorCorrectionLevel(String errorCorrectionLevel) {
        this.errorCorrectionLevel = errorCorrectionLevel;
    }
    
    @Override
    public String toString() {
        return "QRCodeRequest{" +
                "content='" + getContent() + '\'' +
                ", format='" + getFormat() + '\'' +
                ", size=" + size +
                ", errorCorrectionLevel='" + errorCorrectionLevel + '\'' +
                '}';
    }
}
