package com.hemakesh.qrcode.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * Base request DTO for code generation
 */
public class CodeGenerationRequest {
    
    @NotBlank(message = "Content cannot be empty")
    @Size(max = 2000, message = "Content cannot exceed 2000 characters")
    private String content;
    
    @Pattern(regexp = "PNG|SVG", message = "Format must be either PNG or SVG")
    private String format = "PNG";
    
    private Integer width;
    private Integer height;
    
    // Constructors
    public CodeGenerationRequest() {}
    
    public CodeGenerationRequest(String content, String format) {
        this.content = content;
        this.format = format;
    }
    
    // Getters and Setters
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getFormat() {
        return format;
    }
    
    public void setFormat(String format) {
        this.format = format;
    }
    
    public Integer getWidth() {
        return width;
    }
    
    public void setWidth(Integer width) {
        this.width = width;
    }
    
    public Integer getHeight() {
        return height;
    }
    
    public void setHeight(Integer height) {
        this.height = height;
    }
    
    @Override
    public String toString() {
        return "CodeGenerationRequest{" +
                "content='" + content + '\'' +
                ", format='" + format + '\'' +
                ", width=" + width +
                ", height=" + height +
                '}';
    }
}
