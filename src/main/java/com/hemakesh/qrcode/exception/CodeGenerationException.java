package com.hemakesh.qrcode.exception;

/**
 * Custom exception for code generation errors
 */
public class CodeGenerationException extends RuntimeException {
    
    private final String errorCode;
    
    public CodeGenerationException(String message) {
        super(message);
        this.errorCode = "CODE_GENERATION_ERROR";
    }
    
    public CodeGenerationException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public CodeGenerationException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "CODE_GENERATION_ERROR";
    }
    
    public CodeGenerationException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
