package com.hemakesh.qrcode.exception;

/**
 * Custom exception for validation errors
 */
public class ValidationException extends RuntimeException {
    
    private final String field;
    private final Object rejectedValue;
    
    public ValidationException(String message) {
        super(message);
        this.field = null;
        this.rejectedValue = null;
    }
    
    public ValidationException(String message, String field, Object rejectedValue) {
        super(message);
        this.field = field;
        this.rejectedValue = rejectedValue;
    }
    
    public ValidationException(String message, Throwable cause) {
        super(message, cause);
        this.field = null;
        this.rejectedValue = null;
    }
    
    public String getField() {
        return field;
    }
    
    public Object getRejectedValue() {
        return rejectedValue;
    }
}
