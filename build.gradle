plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.0'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.hemakesh'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'

	// QR Code and Barcode generation
	implementation 'com.google.zxing:core:3.5.3'
	implementation 'com.google.zxing:javase:3.5.3'

	// SVG support
	implementation 'org.apache.xmlgraphics:batik-transcoder:1.17'
	implementation 'org.apache.xmlgraphics:batik-codec:1.17'
	implementation 'org.apache.xmlgraphics:batik-svg-dom:1.17'
	implementation 'org.apache.xmlgraphics:batik-svggen:1.17'

	// API Documentation
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'

	// Logging
	implementation 'org.springframework.boot:spring-boot-starter-logging'

	// Testing
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.boot:spring-boot-starter-web'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
	useJUnitPlatform()
}
